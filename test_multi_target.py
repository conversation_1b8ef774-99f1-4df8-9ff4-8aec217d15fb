#!/usr/bin/env python3
"""
Test Multi-Target Predictions with Existing Data
"""

import numpy as np
import matplotlib.pyplot as plt
import pickle
from neuralNetwork import prepare_fouling_data, create_fouling_prediction_model

def test_multi_target_predictions():
    """
    Test the multi-target model with existing test data
    """
    print("🔥 Testing Multi-Target Heat Exchanger Predictions")
    print("=" * 60)
    
    # Load and prepare data
    print("📊 Loading and preparing data...")
    try:
        (X_train, y_train, X_val, y_val, X_test, y_test,
         scaler_X, scaler_y) = prepare_fouling_data('data_template_80_90.csv', lookback_days=14)
        
        print(f"✅ Data loaded successfully!")
        print(f"   Test samples: {X_test.shape[0]}")
        print(f"   Features: {X_test.shape[1]}")
        print(f"   Targets: {y_test.shape[1]}")
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return
    
    # Create and load model
    print("\n🤖 Loading trained model...")
    try:
        model = create_fouling_prediction_model(X_test.shape[1])
        model.load('fouling_prediction_model.model')
        print("✅ Model loaded successfully!")
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    # Make predictions on test set
    print("\n🔮 Making predictions on test data...")
    predictions_scaled = model.predict(X_test)
    
    # Inverse transform to original scale
    predictions = scaler_y.inverse_transform(predictions_scaled)
    y_test_original = scaler_y.inverse_transform(y_test)
    
    # Display sample predictions
    print("\n📊 Sample Predictions (first 5 samples):")
    print("=" * 80)
    print("Sample | Fouling Resistance | Q Shell Side | Q Tube Side")
    print("       |   Actual | Predicted |  Actual | Predicted |  Actual | Predicted")
    print("-" * 80)
    
    for i in range(min(5, len(predictions))):
        print(f"{i+1:6d} | {y_test_original[i, 0]:8.6f} | {predictions[i, 0]:9.6f} | "
              f"{y_test_original[i, 1]:7.1f} | {predictions[i, 1]:9.1f} | "
              f"{y_test_original[i, 2]:7.1f} | {predictions[i, 2]:9.1f}")
    
    # Calculate metrics for each target
    target_names = ['Fouling Resistance (m² °C/W)', 'Q Shell Side (kW)', 'Q Tube Side (kW)']
    
    print(f"\n📈 Performance Metrics:")
    print("=" * 60)
    
    for i, target_name in enumerate(target_names):
        mse = np.mean((predictions[:, i] - y_test_original[:, i]) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(predictions[:, i] - y_test_original[:, i]))
        
        print(f"\n{target_name}:")
        print(f"  Mean Squared Error:  {mse:.6f}")
        print(f"  Root Mean Squared Error: {rmse:.6f}")
        print(f"  Mean Absolute Error: {mae:.6f}")
    
    # Create visualizations
    print("\n📊 Creating visualizations...")
    create_comparison_plots(y_test_original, predictions, target_names)
    
    print("\n🎉 Multi-target testing complete!")

def create_comparison_plots(y_actual, y_predicted, target_names):
    """
    Create comparison plots for actual vs predicted values
    """
    # Figure 1: Predictions vs Actual for each target
    fig1, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig1.suptitle('Multi-Target Predictions vs Actual Values', fontsize=16, fontweight='bold')
    
    colors = ['red', 'blue', 'green']
    
    for i, (target_name, color) in enumerate(zip(target_names, colors)):
        ax = axes[i]
        
        # Scatter plot
        ax.scatter(y_actual[:, i], y_predicted[:, i], alpha=0.6, color=color, s=50)
        
        # Perfect prediction line
        min_val = min(y_actual[:, i].min(), y_predicted[:, i].min())
        max_val = max(y_actual[:, i].max(), y_predicted[:, i].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')
        
        ax.set_xlabel(f'Actual {target_name}')
        ax.set_ylabel(f'Predicted {target_name}')
        ax.set_title(f'{target_name}')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Figure 2: Time series comparison (first 20 samples)
    fig2, axes = plt.subplots(3, 1, figsize=(15, 12))
    fig2.suptitle('Time Series Comparison: Actual vs Predicted (First 20 Samples)', fontsize=16, fontweight='bold')
    
    n_samples = min(20, len(y_actual))
    sample_indices = range(n_samples)
    
    for i, (target_name, color) in enumerate(zip(target_names, colors)):
        ax = axes[i]
        
        ax.plot(sample_indices, y_actual[:n_samples, i], 'o-', label='Actual', 
                color=color, linewidth=2, markersize=6, alpha=0.8)
        ax.plot(sample_indices, y_predicted[:n_samples, i], 's--', label='Predicted', 
                color='orange', linewidth=2, markersize=6, alpha=0.8)
        
        ax.set_xlabel('Sample Index')
        ax.set_ylabel(target_name)
        ax.set_title(f'{target_name} - Time Series Comparison')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_xticks(range(0, n_samples, 2))
    
    plt.tight_layout()
    plt.show()
    
    # Figure 3: Error distribution
    fig3, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig3.suptitle('Prediction Error Distribution', fontsize=16, fontweight='bold')
    
    for i, (target_name, color) in enumerate(zip(target_names, colors)):
        ax = axes[i]
        
        errors = y_predicted[:, i] - y_actual[:, i]
        
        ax.hist(errors, bins=15, alpha=0.7, color=color, edgecolor='black')
        ax.axvline(x=0, color='red', linestyle='--', linewidth=2, label='Zero Error')
        ax.axvline(x=np.mean(errors), color='orange', linestyle='-', linewidth=2, 
                  label=f'Mean Error: {np.mean(errors):.4f}')
        
        ax.set_xlabel('Prediction Error')
        ax.set_ylabel('Frequency')
        ax.set_title(f'{target_name} - Error Distribution')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    test_multi_target_predictions()
