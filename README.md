# Fouling Factor Prediction Neural Network

## Overview
This project implements a deep neural network to predict fouling factor in heat exchangers after two weeks, using historical operational data. The model uses a time series approach with a 14-day lookback window to make predictions.

## Data Structure
The input data (`DATA test.csv`) contains:
- **Date**: Timestamp of measurements
- **Unit Charge (%)**: Operating capacity percentage
- **Shell Side**: Reactor effluent flow (kg/h), inlet temperature (°C), outlet temperature (°C)
- **Tube Side**: Reactor feed flow (kg/h), inlet temperature (°C), outlet temperature (°C)
- **Fouling Resistance (m² °C/W)**: Target variable to predict

## Data Preprocessing
1. **Time Series Creation**: Uses 14-day historical data windows to predict fouling factor
2. **Data Splitting**: 
   - Training: 60% (1,674 samples)
   - Validation: 20% (559 samples)
   - Testing: 20% (559 samples)
3. **Feature Scaling**: StandardScaler normalization for both features and targets
4. **Feature Engineering**: Flattened time series data (98 features: 14 days × 7 variables)

## Neural Network Architecture
Based on neural network best practices for regression tasks:

```
Input Layer: 98 neurons (14 days × 7 features)
    ↓
Hidden Layer 1: 128 neurons (ReLU + Dropout 0.3)
    ↓
Hidden Layer 2: 64 neurons (ReLU + Dropout 0.3)
    ↓
Hidden Layer 3: 32 neurons (ReLU + Dropout 0.2)
    ↓
Hidden Layer 4: 16 neurons (ReLU + Dropout 0.1)
    ↓
Output Layer: 1 neuron (Linear activation)
```

### Key Design Decisions:
- **Funnel Architecture**: Decreasing layer sizes to progressively extract higher-level features
- **ReLU Activation**: For hidden layers to handle non-linear relationships
- **Linear Output**: Appropriate for regression tasks
- **Dropout Regularization**: Prevents overfitting (rates: 0.3, 0.3, 0.2, 0.1)
- **L2 Regularization**: Weight decay (5e-4) for additional overfitting prevention

## Training Configuration
- **Loss Function**: Mean Squared Error (MSE)
- **Optimizer**: Adam (learning_rate=0.001, decay=1e-7)
- **Batch Size**: 32
- **Epochs**: 100
- **Accuracy Metric**: Regression accuracy with precision tolerance

## Results
The model achieved excellent performance:
- **Mean Squared Error**: 0.000001
- **Root Mean Squared Error**: 0.000764
- **Mean Absolute Error**: 0.000670

## Visualization
The training process includes:
1. **Training History Plots**: Loss and accuracy curves for training and validation sets
2. **Prediction Analysis**: 
   - Scatter plot of predicted vs actual values
   - Residual analysis
   - Time series comparison
   - Error distribution histogram

## Files Generated
- `fouling_prediction_model.model`: Trained neural network model
- `scalers.pkl`: Feature and target scalers for preprocessing
- Training and validation plots (displayed during execution)

## Usage
Run the main script:
```bash
python3 neuralNetwork.py
```

The script will:
1. Load and preprocess the data
2. Create the neural network architecture
3. Train the model with validation
4. Display training progress and plots
5. Evaluate on test set
6. Save the trained model and scalers

## Key Features
- **Time Series Prediction**: Uses 14-day historical windows
- **Robust Architecture**: Deep network with regularization
- **Comprehensive Evaluation**: Multiple metrics and visualizations
- **Model Persistence**: Saves trained model for future use
- **Real-time Monitoring**: Training progress with loss/accuracy tracking

## Technical Implementation
- Built from scratch using NumPy and custom neural network classes
- Implements forward and backward propagation
- Includes dropout, batch processing, and various optimizers
- Uses matplotlib for comprehensive visualization
- Scikit-learn for data preprocessing and splitting
