#!/usr/bin/env python3
"""
Simple script to test the trained fouling factor prediction model on new data
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import pickle
import sys
import os

# Add the current directory to Python path to import from neuralNetwork
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from neuralNetwork import Model, load_fouling_data, create_time_series_data, FoulingModel


def test_new_data_file(csv_file_path):
    """
    Test the model on a new CSV file with the same format as training data
    
    Parameters:
    - csv_file_path: Path to CSV file with new data
    
    Returns:
    - predictions: Array of predicted fouling factors
    - success: <PERSON><PERSON><PERSON> indicating if test was successful
    """
    print(f"Testing model on: {csv_file_path}")
    print("=" * 50)
    
    try:
        # Load the trained model
        print("Loading trained model...")
        model = Model.load('fouling_prediction_model.model')
        
        # Load the scalers
        print("Loading scalers...")
        with open('scalers.pkl', 'rb') as f:
            scalers = pickle.load(f)
        scaler_X = scalers['scaler_X']
        scaler_y = scalers['scaler_y']
        
        # Load new data
        print("Loading new data...")
        new_data = load_fouling_data(csv_file_path)
        print(f"Loaded {len(new_data)} data points")
        
        if len(new_data) < 14:
            print("❌ Error: Need at least 14 days of data for prediction")
            return None, False
        
        # Create time series data
        print("Creating time series sequences...")
        X_new, _ = create_time_series_data(new_data, lookback_days=14)
        X_new_flattened = X_new.reshape(X_new.shape[0], -1)
        
        # Scale features
        X_new_scaled = scaler_X.transform(X_new_flattened)
        
        # Make predictions
        print("Making predictions...")
        predictions_scaled = model.predict(X_new_scaled)
        predictions = scaler_y.inverse_transform(predictions_scaled).flatten()
        
        print(f"✅ Successfully generated {len(predictions)} predictions!")
        
        # Display first few predictions
        print("\nFirst 10 Predictions:")
        print("-" * 30)
        for i in range(min(10, len(predictions))):
            print(f"Day {i+15:2d}: {predictions[i]:.6f}")
        
        if len(predictions) > 10:
            print(f"... and {len(predictions) - 10} more predictions")
        
        # Plot results
        plt.figure(figsize=(12, 6))
        days = range(15, 15 + len(predictions))
        plt.plot(days, predictions, 'b-', marker='o', markersize=4, linewidth=2)
        plt.xlabel('Day')
        plt.ylabel('Predicted Fouling Factor (m² °C/W)')
        plt.title('Fouling Factor Predictions')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        return predictions, True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, False


def test_manual_data():
    """
    Test the model with manually entered data
    """
    print("Manual Data Entry Mode")
    print("=" * 50)
    print("Enter 14 days of operational data:")
    print("Format: Unit_Charge, Shell_Flow, Shell_Temp_In, Shell_Temp_Out, Tube_Flow, Tube_Temp_In, Tube_Temp_Out")
    print("Example: 85.5, 1200.0, 180.5, 175.2, 800.0, 25.0, 45.3")
    print()
    
    data = []
    for day in range(1, 15):
        while True:
            try:
                user_input = input(f"Day {day:2d}: ")
                if user_input.lower() in ['quit', 'exit', 'q']:
                    return
                
                values = [float(x.strip()) for x in user_input.split(',')]
                if len(values) != 7:
                    print("❌ Please enter exactly 7 values separated by commas")
                    continue
                
                data.append(values)
                break
                
            except ValueError:
                print("❌ Please enter valid numbers separated by commas")
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                return
    
    # Convert to numpy array
    recent_data = np.array(data)
    
    try:
        # Load model and scalers
        model = Model.load('fouling_prediction_model.model')
        with open('scalers.pkl', 'rb') as f:
            scalers = pickle.load(f)
        scaler_X = scalers['scaler_X']
        scaler_y = scalers['scaler_y']
        
        # Prepare and scale data
        X_single = recent_data.flatten().reshape(1, -1)
        X_single_scaled = scaler_X.transform(X_single)
        
        # Make prediction
        prediction_scaled = model.predict(X_single_scaled)
        prediction = scaler_y.inverse_transform(prediction_scaled).flatten()[0]
        
        print(f"\n🎯 Predicted fouling factor: {prediction:.6f}")
        
    except Exception as e:
        print(f"❌ Error making prediction: {e}")


def create_sample_data():
    """
    Create a sample CSV file for testing
    """
    print("Creating sample test data...")
    
    # Generate realistic sample data
    np.random.seed(42)  # For reproducible results
    
    # Base values with some variation
    unit_charge = 85 + np.random.normal(0, 5, 30)
    shell_flow = 1200 + np.random.normal(0, 100, 30)
    shell_temp_in = 180 + np.random.normal(0, 5, 30)
    shell_temp_out = 175 + np.random.normal(0, 5, 30)
    tube_flow = 800 + np.random.normal(0, 50, 30)
    tube_temp_in = 25 + np.random.normal(0, 2, 30)
    tube_temp_out = 45 + np.random.normal(0, 3, 30)
    
    # Create CSV content
    csv_content = "Date;Unit Charge (%);Shell Side: Reactor Effluent Flow (kg/h);Shell Side: Temp In (°C);Shell Side: Temp Out (°C);Tube Side: Reactor Feed Flow (kg/h);Tube Side: Temp In (°C);Tube Side: Temp Out (°C);Fouling Resistance (m² °C/W)\n"
    csv_content += ";;;;;;;\n"
    csv_content += ";;;;;;;\n"
    
    for i in range(30):
        csv_content += f"2024-01-{i+1:02d};{unit_charge[i]:.1f};{shell_flow[i]:.1f};{shell_temp_in[i]:.1f};{shell_temp_out[i]:.1f};{tube_flow[i]:.1f};{tube_temp_in[i]:.1f};{tube_temp_out[i]:.1f};0.000500\n"
    
    with open('sample_test_data.csv', 'w') as f:
        f.write(csv_content)
    
    print("✅ Created 'sample_test_data.csv' with 30 days of sample data")
    return 'sample_test_data.csv'


def main():
    """
    Main function with user menu
    """
    print("🔬 Fouling Factor Prediction Model Tester")
    print("=" * 50)
    
    while True:
        print("\nChoose an option:")
        print("1. Test with CSV file")
        print("2. Test with manual data entry")
        print("3. Create sample test data")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            csv_file = input("Enter CSV file path: ").strip()
            if csv_file:
                test_new_data_file(csv_file)
            else:
                print("❌ Please provide a valid file path")
                
        elif choice == '2':
            test_manual_data()
            
        elif choice == '3':
            sample_file = create_sample_data()
            test_it = input("Test with the created sample data? (y/n): ").strip().lower()
            if test_it in ['y', 'yes']:
                test_new_data_file(sample_file)
                
        elif choice == '4':
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")


if __name__ == "__main__":
    main()
