#!/usr/bin/env python3
"""
Debug script to check data shapes
"""

import numpy as np
from neuralNetwork import load_fouling_data, create_time_series_data, prepare_fouling_data

def debug_data_shapes():
    """
    Debug the data shapes to understand the issue
    """
    print("🔍 Debugging Data Shapes")
    print("=" * 50)
    
    # Load raw data
    print("Loading raw data...")
    raw_data = load_fouling_data('data_template_80_90.csv')
    print(f"Raw data shape: {raw_data.shape}")
    print(f"Raw data sample (first row): {raw_data[0]}")
    
    # Create time series data
    print("\nCreating time series data...")
    X, y = create_time_series_data(raw_data, lookback_days=14)
    print(f"X shape: {X.shape}")
    print(f"y shape: {y.shape}")
    print(f"X sample shape: {X[0].shape}")
    print(f"y sample: {y[0]}")
    
    # Check the full pipeline
    print("\nTesting full pipeline...")
    try:
        (X_train, y_train, X_val, y_val, X_test, y_test,
         scaler_X, scaler_y) = prepare_fouling_data('data_template_80_90.csv', lookback_days=14)
        
        print(f"X_train shape: {X_train.shape}")
        print(f"y_train shape: {y_train.shape}")
        print(f"X_val shape: {X_val.shape}")
        print(f"y_val shape: {y_val.shape}")
        print(f"X_test shape: {X_test.shape}")
        print(f"y_test shape: {y_test.shape}")
        
        print(f"\nSample y_train values: {y_train[0]}")
        print(f"y_train min/max per column:")
        for i in range(y_train.shape[1]):
            print(f"  Column {i}: min={y_train[:, i].min():.6f}, max={y_train[:, i].max():.6f}")
        
    except Exception as e:
        print(f"Error in pipeline: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data_shapes()
