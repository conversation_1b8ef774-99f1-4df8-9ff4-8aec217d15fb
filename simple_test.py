#!/usr/bin/env python3
"""
Simple test script for the fouling factor prediction model
"""

import numpy as np
import matplotlib.pyplot as plt
import pickle
import os

# Check if model files exist
if not os.path.exists('fouling_prediction_model.model'):
    print("❌ Model file not found. Please run training first:")
    print("   python3 neuralNetwork.py")
    exit(1)

if not os.path.exists('scalers.pkl'):
    print("❌ Scalers file not found. Please run training first:")
    print("   python3 neuralNetwork.py")
    exit(1)

# Import the Model class by running the neuralNetwork module
import neuralNetwork
from neuralNetwork import Model

def test_with_sample_data():
    """
    Test the model with sample data
    """
    print("🧪 Testing Fouling Factor Prediction Model")
    print("=" * 50)
    
    # Sample 14 days of operational data
    # Format: [Unit_Charge(%), Shell_Flow(kg/h), Shell_Temp_In(°C), Shell_Temp_Out(°C), 
    #          Tube_Flow(kg/h), <PERSON>be_Temp_In(°C), Tube_Temp_Out(°C)]
    sample_data = np.array([
        [85.2, 1205.3, 182.1, 176.8, 798.5, 24.8, 44.2],  # Day 1
        [84.8, 1198.7, 181.5, 176.2, 802.1, 25.1, 44.8],  # Day 2
        [86.1, 1212.4, 183.2, 177.5, 795.3, 24.5, 43.9],  # Day 3
        [85.5, 1201.8, 182.8, 177.1, 799.7, 25.3, 45.1],  # Day 4
        [84.9, 1195.2, 181.9, 176.4, 803.2, 24.9, 44.5],  # Day 5
        [85.8, 1208.6, 182.5, 177.0, 797.8, 25.0, 44.7],  # Day 6
        [86.3, 1215.1, 183.7, 178.2, 794.6, 24.7, 43.8],  # Day 7
        [85.1, 1199.4, 182.0, 176.7, 801.5, 25.2, 44.9],  # Day 8
        [84.7, 1193.8, 181.3, 175.9, 804.8, 24.8, 44.3],  # Day 9
        [85.9, 1209.7, 182.9, 177.4, 796.2, 25.1, 44.6],  # Day 10
        [86.0, 1211.3, 183.1, 177.8, 795.9, 24.9, 44.1],  # Day 11
        [85.4, 1203.5, 182.3, 176.9, 798.1, 25.0, 44.8],  # Day 12
        [84.6, 1191.2, 181.1, 175.7, 805.3, 24.7, 44.2],  # Day 13
        [85.7, 1207.8, 182.7, 177.2, 797.4, 25.2, 44.9],  # Day 14
    ])
    
    print(f"Sample data shape: {sample_data.shape}")
    print("Testing with 14 days of operational data...")
    
    try:
        # Load the trained model
        print("\n📥 Loading trained model...")
        model = Model.load('fouling_prediction_model.model')
        print("✅ Model loaded successfully!")
        
        # Load the scalers
        print("📥 Loading scalers...")
        with open('scalers.pkl', 'rb') as f:
            scalers = pickle.load(f)
        scaler_X = scalers['scaler_X']
        scaler_y = scalers['scaler_y']
        print("✅ Scalers loaded successfully!")
        
        # Prepare the data (flatten 14x7 to 98 features)
        X_test = sample_data.flatten().reshape(1, -1)
        print(f"📊 Prepared data shape: {X_test.shape}")
        
        # Scale the features
        X_test_scaled = scaler_X.transform(X_test)
        print("✅ Data scaled using training scalers")
        
        # Make prediction
        print("\n🔮 Making prediction...")
        prediction_scaled = model.predict(X_test_scaled)
        
        # Convert back to original scale
        prediction = scaler_y.inverse_transform(prediction_scaled).flatten()[0]
        
        print(f"🎯 Predicted fouling factor: {prediction:.6f} m² °C/W")
        
        # Interpretation
        print("\n📊 Interpretation:")
        if prediction < 0.0005:
            print("   ✅ Low fouling - Good heat exchanger performance")
            status = "Good"
        elif prediction < 0.001:
            print("   ⚠️  Moderate fouling - Monitor closely")
            status = "Monitor"
        else:
            print("   🚨 High fouling - Consider cleaning or maintenance")
            status = "Action Required"
        
        # Create a simple visualization
        plt.figure(figsize=(10, 6))
        
        # Plot 1: Sample data overview
        plt.subplot(2, 2, 1)
        days = range(1, 15)
        plt.plot(days, sample_data[:, 0], 'b-o', label='Unit Charge (%)')
        plt.xlabel('Day')
        plt.ylabel('Unit Charge (%)')
        plt.title('Unit Charge Over 14 Days')
        plt.grid(True, alpha=0.3)
        
        # Plot 2: Temperature data
        plt.subplot(2, 2, 2)
        plt.plot(days, sample_data[:, 2], 'r-o', label='Shell Temp In')
        plt.plot(days, sample_data[:, 3], 'b-o', label='Shell Temp Out')
        plt.xlabel('Day')
        plt.ylabel('Temperature (°C)')
        plt.title('Shell Side Temperatures')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot 3: Flow rates
        plt.subplot(2, 2, 3)
        plt.plot(days, sample_data[:, 1], 'g-o', label='Shell Flow')
        plt.plot(days, sample_data[:, 4], 'm-o', label='Tube Flow')
        plt.xlabel('Day')
        plt.ylabel('Flow Rate (kg/h)')
        plt.title('Flow Rates')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot 4: Prediction result
        plt.subplot(2, 2, 4)
        colors = {'Good': 'green', 'Monitor': 'orange', 'Action Required': 'red'}
        plt.bar(['Prediction'], [prediction], color=colors[status], alpha=0.7)
        plt.ylabel('Fouling Factor (m² °C/W)')
        plt.title(f'Prediction: {status}')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        return prediction
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None


def create_your_own_test():
    """
    Template for creating your own test
    """
    print("\n" + "="*60)
    print("📝 How to Test with Your Own Data")
    print("="*60)
    
    print("""
To test with your own data, replace the sample_data array with your measurements:

your_data = np.array([
    [day1_unit_charge, day1_shell_flow, day1_shell_temp_in, day1_shell_temp_out, day1_tube_flow, day1_tube_temp_in, day1_tube_temp_out],
    [day2_unit_charge, day2_shell_flow, day2_shell_temp_in, day2_shell_temp_out, day2_tube_flow, day2_tube_temp_in, day2_tube_temp_out],
    # ... continue for 14 days total
])

Variable meanings:
• unit_charge: Operating capacity percentage (%)
• shell_flow: Reactor effluent flow rate (kg/h)  
• shell_temp_in: Shell side inlet temperature (°C)
• shell_temp_out: Shell side outlet temperature (°C)
• tube_flow: Reactor feed flow rate (kg/h)
• tube_temp_in: Tube side inlet temperature (°C)
• tube_temp_out: Tube side outlet temperature (°C)

Then run the same prediction code as shown above.
""")


def batch_test_example():
    """
    Example of testing multiple time windows
    """
    print("\n" + "="*60)
    print("📈 Batch Testing Example")
    print("="*60)
    
    # Generate 20 days of sample data
    np.random.seed(42)
    base_values = np.array([85.0, 1200.0, 182.0, 177.0, 800.0, 25.0, 45.0])
    
    all_data = []
    for day in range(20):
        # Add realistic daily variations
        daily_variation = np.random.normal(0, [2, 50, 2, 2, 30, 1, 2])
        # Add slight degradation trend
        degradation = np.array([0, 0, 0.05, 0.05, 0, 0, 0]) * day
        
        daily_data = base_values + daily_variation + degradation
        all_data.append(daily_data)
    
    all_data = np.array(all_data)
    
    try:
        # Load model and scalers
        model = Model.load('fouling_prediction_model.model')
        with open('scalers.pkl', 'rb') as f:
            scalers = pickle.load(f)
        scaler_X = scalers['scaler_X']
        scaler_y = scalers['scaler_y']
        
        # Make predictions for each 14-day window
        predictions = []
        prediction_days = []
        
        print("Making predictions for multiple time windows...")
        for i in range(14, 20):  # Days 15-20
            window_data = all_data[i-14:i]  # 14 days ending at day i
            
            X_window = window_data.flatten().reshape(1, -1)
            X_window_scaled = scaler_X.transform(X_window)
            
            pred_scaled = model.predict(X_window_scaled)
            pred = scaler_y.inverse_transform(pred_scaled).flatten()[0]
            
            predictions.append(pred)
            prediction_days.append(i + 1)
        
        print(f"\n🎯 Predictions for days {prediction_days[0]}-{prediction_days[-1]}:")
        print("-" * 40)
        for day, pred in zip(prediction_days, predictions):
            print(f"Day {day:2d}: {pred:.6f} m² °C/W")
        
        # Trend analysis
        if len(predictions) > 1:
            trend = predictions[-1] - predictions[0]
            print(f"\n📊 Trend Analysis:")
            print(f"First prediction: {predictions[0]:.6f}")
            print(f"Last prediction:  {predictions[-1]:.6f}")
            print(f"Change: {trend:+.6f} ({'⬆️ increasing' if trend > 0 else '⬇️ decreasing'} fouling)")
        
        # Plot the trend
        plt.figure(figsize=(10, 6))
        plt.plot(prediction_days, predictions, 'b-o', linewidth=2, markersize=8)
        plt.xlabel('Day')
        plt.ylabel('Predicted Fouling Factor (m² °C/W)')
        plt.title('Fouling Factor Trend Over Time')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"❌ Error in batch testing: {e}")


if __name__ == "__main__":
    # Run the main test
    prediction = test_with_sample_data()
    
    if prediction is not None:
        # Show additional examples
        create_your_own_test()
        batch_test_example()
    
    print("\n🎉 Testing complete!")
    print("\nNext steps:")
    print("1. Replace sample data with your actual measurements")
    print("2. Run this script again to get real predictions")
    print("3. Set up automated monitoring with your plant data")
