# 🧪 Testing Guide: Fouling Factor Prediction Model

## Quick Start

After training your model, you have several ways to test it with new data:

### Method 1: Simple Testing (Recommended)
```bash
python3 predict_fouling.py
```
This runs a complete test with sample data and shows:
- Prediction results with interpretation
- Comprehensive visualizations
- Usage instructions for your own data

### Method 2: Command Line Testing
```bash
# Test with a new CSV file
python3 neuralNetwork.py test your_new_data.csv

# See usage examples
python3 neuralNetwork.py example
```

### Method 3: Advanced Testing
```bash
python3 test_predictions.py
```
Shows multiple testing scenarios and comparisons.

## 📊 Data Format Requirements

### For CSV Files
Your CSV file must have the same format as the training data:
```csv
Date;Unit Charge (%);Shell Side: Reactor Effluent Flow (kg/h);Shell Side: Temp In (°C);Shell Side: Temp Out (°C);Tube Side: Reactor Feed Flow (kg/h);Tube Side: Temp In (°C);Tube Side: Temp Out (°C);Fouling Resistance (m² °C/W)
;;;;;;
;;;;;;
2024-01-01;85.2;1205.3;182.1;176.8;798.5;24.8;44.2;0.000500
2024-01-02;84.8;1198.7;181.5;176.2;802.1;25.1;44.8;0.000510
...
```

**Important Notes:**
- Need at least 14 days of data (model uses 14-day lookback)
- The last column (Fouling Resistance) can be dummy values for new predictions
- Semicolon (`;`) separated values
- Skip first 3 header rows

### For Manual Data Entry
Each day needs 7 values in this order:
1. **Unit Charge (%)**: Operating capacity percentage
2. **Shell Flow (kg/h)**: Reactor effluent flow rate  
3. **Shell Temp In (°C)**: Shell side inlet temperature
4. **Shell Temp Out (°C)**: Shell side outlet temperature
5. **Tube Flow (kg/h)**: Reactor feed flow rate
6. **Tube Temp In (°C)**: Tube side inlet temperature
7. **Tube Temp Out (°C)**: Tube side outlet temperature

Example for one day: `85.2, 1205.3, 182.1, 176.8, 798.5, 24.8, 44.2`

## 🔧 Programming Interface

### Single Prediction (Easy Method)
```python
import numpy as np
from predict_fouling import predict_fouling_factor

# Your 14 days of data (14 rows × 7 columns)
your_data = np.array([
    [85.2, 1205.3, 182.1, 176.8, 798.5, 24.8, 44.2],  # Day 1
    [84.8, 1198.7, 181.5, 176.2, 802.1, 25.1, 44.8],  # Day 2
    # ... continue for 12 more days (total 14 days)
])

# Make prediction
prediction = predict_fouling_factor(your_data)
print(f"Predicted fouling factor: {prediction:.6f} m² °C/W")
```

### Batch Predictions from CSV
```python
# Method 1: Command line
python3 neuralNetwork.py test your_data.csv

# Method 2: Python code
from neuralNetwork import test_new_data
predictions, dates = test_new_data('your_data.csv')
if predictions is not None:
    for i, pred in enumerate(predictions[:5]):  # Show first 5
        print(f"Day {dates[i]}: {pred:.6f}")
```

## 📈 Understanding Results

### Fouling Factor Interpretation
- **< 0.0005**: Low fouling - Good performance
- **0.0005 - 0.001**: Moderate fouling - Monitor closely  
- **> 0.001**: High fouling - Consider maintenance

### Typical Values
Based on your training data, typical fouling factors range from ~0.0002 to ~0.002 m² °C/W.

## 🚨 Common Issues & Solutions

### "Need at least 14 days of data"
**Problem**: Your data has fewer than 14 rows.
**Solution**: The model needs 14 consecutive days to make a prediction.

### "Model files not found"
**Problem**: Missing `fouling_prediction_model.model` or `scalers.pkl`.
**Solution**: Run training first: `python3 neuralNetwork.py`

### "Error loading data"
**Problem**: CSV format doesn't match expected format.
**Solution**: 
- Check semicolon separation
- Ensure 3 header rows
- Verify 9 columns (including date and fouling resistance)

### Unrealistic Predictions
**Problem**: Predictions seem too high/low.
**Solution**: 
- Check data units match training data
- Verify temperature units (°C)
- Ensure flow rates are in kg/h

## 📁 File Structure
After training, you should have:
```
├── neuralNetwork.py          # Main training script
├── predict_fouling.py       # Simple prediction script (RECOMMENDED)
├── test_predictions.py      # Advanced testing with multiple scenarios
├── test_model.py            # Interactive testing script
├── example_test.py          # Code examples
├── fouling_prediction_model.model  # Trained model
├── scalers.pkl              # Data scalers
├── DATA test.csv            # Original training data
└── TESTING_GUIDE.md         # This guide
```

## 🎯 Example Workflows

### Workflow 1: Quick Testing (Recommended)
1. Run: `python3 predict_fouling.py`
2. View the sample prediction and visualization
3. Replace sample data with your actual 14 days of measurements
4. Run again to get your real prediction

### Workflow 2: Test New Plant Data from CSV
1. Export your plant data to CSV (same format as training)
2. Run: `python3 neuralNetwork.py test your_data.csv`
3. View predictions and trend analysis

### Workflow 3: Daily Monitoring
1. Collect 14 days of operational data
2. Use `predict_fouling.py` or the programming interface for automated predictions
3. Set up alerts if fouling factor exceeds thresholds (> 0.001)
4. Update predictions daily with rolling 14-day windows

### Workflow 4: What-If Analysis
1. Create hypothetical operational scenarios in `predict_fouling.py`
2. Run `python3 test_predictions.py` for multiple scenario comparisons
3. Compare predictions under different operating conditions
4. Optimize operations to minimize fouling

## 🔍 Validation Tips

1. **Sanity Check**: Compare predictions with historical fouling patterns
2. **Trend Analysis**: Look for realistic fouling progression over time
3. **Cross-Validation**: Test on known historical periods
4. **Operational Context**: Consider recent maintenance, feed changes, etc.

## 📞 Need Help?

If you encounter issues:
1. Check this guide first
2. Run `python3 example_test.py` to see working examples
3. Verify your data format matches the training data exactly
4. Ensure all required files are present in the same directory
