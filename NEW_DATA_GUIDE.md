# Using new_data.csv for Custom Predictions

## 📁 **File Created: `new_data.csv`**

I've created a new CSV file called `new_data.csv` that you can use to input your own operational data for predictions. The neural network will automatically use this file when you run the test command.

## 🚀 **How to Use**

### **1. Run Predictions with Your Data**
```bash
python3 neuralNetwork.py test
```

The system will:
- ✅ Load data from `new_data.csv`
- ✅ Use the **last 14 days** for prediction (most recent data)
- ✅ Predict all 3 targets: Fouling Resistance, Q Shell Side, Q Tube Side
- ✅ Display results with status interpretation

### **2. CSV File Format**

The `new_data.csv` file uses **semicolon separators** and **comma decimal notation** (European format):

```csv
Date;Unit Charge (%);Shell Flow (kg/h);Shell Temp In (°C);Shell Temp Out (°C);Tube Flow (kg/h);Tube Temp In (°C);Tube Temp Out (°C);Fouling Resistance (m² °C/W);Q Shell Side (kW);Q Tube Side (kW)
2024-01-01;85,5;12000;450;320;12000;190;340;0,005;1900;1850
```

## 📊 **Input Columns (Required)**

| Column | Description | Unit | Typical Range |
|--------|-------------|------|---------------|
| **Date** | Date identifier | YYYY-MM-DD | Any valid date |
| **Unit Charge (%)** | Heat exchanger load | % | 70-95% |
| **Shell Flow (kg/h)** | Shell side flow rate | kg/h | 8,000-16,000 |
| **Shell Temp In (°C)** | Shell inlet temperature | °C | 400-500 |
| **Shell Temp Out (°C)** | Shell outlet temperature | °C | 280-360 |
| **Tube Flow (kg/h)** | Tube side flow rate | kg/h | 8,000-16,000 |
| **Tube Temp In (°C)** | Tube inlet temperature | °C | 150-220 |
| **Tube Temp Out (°C)** | Tube outlet temperature | °C | 300-380 |

## 🎯 **Output Columns (Optional - Can Use Dummy Values)**

| Column | Description | Unit | Note |
|--------|-------------|------|------|
| **Fouling Resistance** | Heat transfer resistance | m² °C/W | **Predicted by AI** |
| **Q Shell Side** | Heat duty shell side | kW | **Predicted by AI** |
| **Q Tube Side** | Heat duty tube side | kW | **Predicted by AI** |

> 💡 **Note**: The output columns can contain dummy values (like 0,005;1900;1850) since the AI will predict the actual values based on the input data.

## ✏️ **How to Edit the File**

### **Option 1: Add New Rows**
Simply add new rows with your operational data:
```csv
2024-01-21;87,2;12300;452;322;12100;189;342;0,005;1900;1850
2024-01-22;85,8;11950;449;319;12000;191;339;0,005;1900;1850
```

### **Option 2: Replace All Data**
Replace the entire content with your own data (minimum 14 rows required).

### **Option 3: Use Excel/LibreOffice**
1. Open `new_data.csv` in Excel or LibreOffice Calc
2. Edit the values as needed
3. Save as CSV with semicolon separators

## 📋 **Requirements**

- **Minimum 14 rows**: The neural network needs 14 consecutive days for prediction
- **Correct format**: Use semicolons (;) as separators and commas (,) for decimals
- **Complete data**: All input columns must have values (no empty cells)

## 🔍 **Example Usage**

### **Sample Input Data (new_data.csv)**
```csv
Date;Unit Charge (%);Shell Flow (kg/h);Shell Temp In (°C);Shell Temp Out (°C);Tube Flow (kg/h);Tube Temp In (°C);Tube Temp Out (°C);Fouling Resistance (m² °C/W);Q Shell Side (kW);Q Tube Side (kW)
2024-01-01;85,5;12000;450;320;12000;190;340;0,005;1900;1850
2024-01-02;86,2;11800;455;325;12200;185;345;0,005;1920;1870
...
2024-01-20;88,1;12400;456;326;12300;186;346;0,005;1940;1890
```

### **Sample Output**
```
✅ PREDICTION RESULTS:
🔴 Fouling Resistance: 0.005428 m² °C/W
🔵 Q Shell Side:       1910.11 kW
🟢 Q Tube Side:        1842.32 kW

📊 Fouling Status: 🟠 FAIR - Moderate fouling, monitor closely
```

## 🎯 **Fouling Status Interpretation**

| Range | Status | Meaning | Action |
|-------|--------|---------|--------|
| < 0.002 | 🟢 EXCELLENT | Very clean heat exchanger | Continue normal operation |
| 0.002-0.005 | 🟡 GOOD | Normal fouling levels | Monitor regularly |
| 0.005-0.010 | 🟠 FAIR | Moderate fouling | Monitor closely, plan cleaning |
| > 0.010 | 🔴 POOR | High fouling | Cleaning recommended |

## 🛠️ **Troubleshooting**

### **Error: "Need at least 14 days of data"**
- **Solution**: Add more rows to `new_data.csv` (minimum 14 required)

### **Error: "Error loading new_data.csv"**
- **Solution**: Check file format (semicolons, commas for decimals)
- **Solution**: Ensure all columns have values

### **Error: "Can't get attribute"**
- **Solution**: Make sure the model is trained first: `python3 neuralNetwork.py`

## 📈 **Advanced Usage**

### **Batch Processing Multiple Scenarios**
1. Create different CSV files: `scenario1.csv`, `scenario2.csv`, etc.
2. Rename the desired file to `new_data.csv`
3. Run `python3 neuralNetwork.py test`

### **Historical Analysis**
- Add historical data with actual dates
- The system will use the most recent 14 days for prediction
- Compare predictions with known outcomes for validation

---

## 🎉 **Ready to Use!**

Your `new_data.csv` file is ready! Simply:
1. ✏️ Edit the file with your operational data
2. 🚀 Run `python3 neuralNetwork.py test`
3. 📊 Get instant predictions for fouling resistance and heat duties

The neural network will automatically handle the data processing and provide comprehensive predictions based on your input data!
