#!/usr/bin/env python3
"""
Example script showing how to test the fouling factor prediction model
"""

import numpy as np
import pickle
from neuralNetwork import Model

def quick_test_example():
    """
    Quick example of how to test the model with your own data
    """
    print("🚀 Quick Test Example")
    print("=" * 50)
    
    # Example: 14 days of operational data
    # Each row represents one day with 7 measurements:
    # [Unit_Charge(%), Shell_Flow(kg/h), Shell_Temp_In(°C), Shell_Temp_Out(°C), 
    #  Tube_Flow(kg/h), Tube_Temp_In(°C), Tube_Temp_Out(°C)]
    
    sample_data = np.array([
        [85.2, 1205.3, 182.1, 176.8, 798.5, 24.8, 44.2],  # Day 1
        [84.8, 1198.7, 181.5, 176.2, 802.1, 25.1, 44.8],  # Day 2
        [86.1, 1212.4, 183.2, 177.5, 795.3, 24.5, 43.9],  # Day 3
        [85.5, 1201.8, 182.8, 177.1, 799.7, 25.3, 45.1],  # Day 4
        [84.9, 1195.2, 181.9, 176.4, 803.2, 24.9, 44.5],  # Day 5
        [85.8, 1208.6, 182.5, 177.0, 797.8, 25.0, 44.7],  # Day 6
        [86.3, 1215.1, 183.7, 178.2, 794.6, 24.7, 43.8],  # Day 7
        [85.1, 1199.4, 182.0, 176.7, 801.5, 25.2, 44.9],  # Day 8
        [84.7, 1193.8, 181.3, 175.9, 804.8, 24.8, 44.3],  # Day 9
        [85.9, 1209.7, 182.9, 177.4, 796.2, 25.1, 44.6],  # Day 10
        [86.0, 1211.3, 183.1, 177.8, 795.9, 24.9, 44.1],  # Day 11
        [85.4, 1203.5, 182.3, 176.9, 798.1, 25.0, 44.8],  # Day 12
        [84.6, 1191.2, 181.1, 175.7, 805.3, 24.7, 44.2],  # Day 13
        [85.7, 1207.8, 182.7, 177.2, 797.4, 25.2, 44.9],  # Day 14
    ])
    
    print("Sample data shape:", sample_data.shape)
    print("This represents 14 days of operational data")
    print()
    
    try:
        # Load the trained model and scalers
        print("Loading model and scalers...")
        model = Model.load('fouling_prediction_model.model')
        
        with open('scalers.pkl', 'rb') as f:
            scalers = pickle.load(f)
        scaler_X = scalers['scaler_X']
        scaler_y = scalers['scaler_y']
        
        # Prepare the data (flatten 14x7 to 98 features)
        X_test = sample_data.flatten().reshape(1, -1)
        print(f"Flattened data shape: {X_test.shape}")
        
        # Scale the features using the same scaler from training
        X_test_scaled = scaler_X.transform(X_test)
        
        # Make prediction
        print("Making prediction...")
        prediction_scaled = model.predict(X_test_scaled)
        
        # Convert back to original scale
        prediction = scaler_y.inverse_transform(prediction_scaled).flatten()[0]
        
        print(f"🎯 Predicted fouling factor: {prediction:.6f} m² °C/W")
        
        # Interpretation
        print("\n📊 Interpretation:")
        if prediction < 0.0005:
            print("   Low fouling - Good heat exchanger performance")
        elif prediction < 0.001:
            print("   Moderate fouling - Monitor closely")
        else:
            print("   High fouling - Consider cleaning or maintenance")
            
    except FileNotFoundError:
        print("❌ Model files not found. Please run the training first:")
        print("   python3 neuralNetwork.py")
    except Exception as e:
        print(f"❌ Error: {e}")


def test_with_your_data():
    """
    Template for testing with your own data
    """
    print("\n" + "="*50)
    print("📝 Template for Your Own Data")
    print("="*50)
    
    print("""
To test with your own data, replace the sample_data array above with your actual measurements:

# Your 14 days of data
your_data = np.array([
    [unit_charge_day1, shell_flow_day1, shell_temp_in_day1, shell_temp_out_day1, tube_flow_day1, tube_temp_in_day1, tube_temp_out_day1],
    [unit_charge_day2, shell_flow_day2, shell_temp_in_day2, shell_temp_out_day2, tube_flow_day2, tube_temp_in_day2, tube_temp_out_day2],
    # ... continue for 14 days
    [unit_charge_day14, shell_flow_day14, shell_temp_in_day14, shell_temp_out_day14, tube_flow_day14, tube_temp_in_day14, tube_temp_out_day14],
])

Variable meanings:
- unit_charge: Operating capacity percentage (%)
- shell_flow: Reactor effluent flow rate (kg/h)
- shell_temp_in: Shell side inlet temperature (°C)
- shell_temp_out: Shell side outlet temperature (°C)
- tube_flow: Reactor feed flow rate (kg/h)
- tube_temp_in: Tube side inlet temperature (°C)
- tube_temp_out: Tube side outlet temperature (°C)

Then run the same prediction code as in the example above.
""")


def batch_predictions_example():
    """
    Example of making multiple predictions from a sequence of data
    """
    print("\n" + "="*50)
    print("📈 Batch Predictions Example")
    print("="*50)
    
    # Simulate 20 days of data
    np.random.seed(123)
    days_of_data = 20
    
    # Generate realistic operational data with some trends
    base_data = np.array([85.0, 1200.0, 182.0, 177.0, 800.0, 25.0, 45.0])
    all_data = []
    
    for day in range(days_of_data):
        # Add some realistic variation and slight degradation over time
        daily_variation = np.random.normal(0, [2, 50, 2, 2, 30, 1, 2])
        degradation = np.array([0, 0, 0.1, 0.1, 0, 0, 0]) * day  # Slight temperature increase over time
        
        daily_data = base_data + daily_variation + degradation
        all_data.append(daily_data)
    
    all_data = np.array(all_data)
    
    print(f"Generated {days_of_data} days of simulated data")
    
    try:
        # Load model and scalers
        model = Model.load('fouling_prediction_model.model')
        with open('scalers.pkl', 'rb') as f:
            scalers = pickle.load(f)
        scaler_X = scalers['scaler_X']
        scaler_y = scalers['scaler_y']
        
        # Make predictions for each possible 14-day window
        predictions = []
        prediction_days = []
        
        for i in range(14, days_of_data):
            # Get 14 days of data ending at day i
            window_data = all_data[i-14:i]
            
            # Flatten and scale
            X_window = window_data.flatten().reshape(1, -1)
            X_window_scaled = scaler_X.transform(X_window)
            
            # Predict
            pred_scaled = model.predict(X_window_scaled)
            pred = scaler_y.inverse_transform(pred_scaled).flatten()[0]
            
            predictions.append(pred)
            prediction_days.append(i + 1)
        
        print(f"\nMade {len(predictions)} predictions:")
        print("-" * 30)
        for day, pred in zip(prediction_days, predictions):
            print(f"Day {day:2d}: {pred:.6f}")
            
        # Simple trend analysis
        if len(predictions) > 1:
            trend = predictions[-1] - predictions[0]
            print(f"\nTrend analysis:")
            print(f"First prediction: {predictions[0]:.6f}")
            print(f"Last prediction:  {predictions[-1]:.6f}")
            print(f"Change: {trend:+.6f} ({'increasing' if trend > 0 else 'decreasing'} fouling)")
            
    except FileNotFoundError:
        print("❌ Model files not found. Please run training first.")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    quick_test_example()
    test_with_your_data()
    batch_predictions_example()
