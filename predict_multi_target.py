#!/usr/bin/env python3
"""
Multi-Target Heat Exchanger Prediction Script
Predicts Fouling Resistance, Q Shell Side, and Q Tube Side
"""

import numpy as np
import matplotlib.pyplot as plt
import pickle
from neuralNetwork import (Model, Layer_Dense, Layer_Input, Activation_ReLU, Activation_Linear,
                          Loss_MeanSquaredError, Optimizer_Adam, Layer_Dropout, Accuracy_Regression)

def load_model_and_scalers():
    """
    Load the trained model and scalers
    """
    try:
        # Load model
        model = Model()
        model.load('fouling_prediction_model.model')
        
        # Load scalers
        with open('scalers.pkl', 'rb') as f:
            scalers = pickle.load(f)
        
        return model, scalers['scaler_X'], scalers['scaler_y']
    except Exception as e:
        print(f"Error loading model or scalers: {e}")
        return None, None, None

def predict_multi_targets(data_14_days):
    """
    Predict multiple targets using 14 days of operational data
    
    Args:
        data_14_days: numpy array of shape (14, 7) containing:
                     [Unit_Charge(%), Shell_Flow(kg/h), Shell_Temp_In(°C), Shell_Temp_Out(°C),
                      Tube_Flow(kg/h), Tube_Temp_In(°C), Tube_Temp_Out(°C)]
    
    Returns:
        predictions: Dictionary containing:
                    - 'fouling_resistance': Predicted fouling factor in m² °C/W
                    - 'q_shell': Predicted Q Shell Side in kW
                    - 'q_tube': Predicted Q Tube Side in kW
    """
    # Load model and scalers
    model, scaler_X, scaler_y = load_model_and_scalers()
    
    if model is None:
        print("❌ Failed to load model. Please train the model first.")
        return None
    
    # Flatten the 14-day data for the neural network
    X_input = data_14_days.reshape(1, -1)  # Shape: (1, 98)
    
    # Scale the input
    X_scaled = scaler_X.transform(X_input)
    
    # Make prediction
    prediction_scaled = model.predict(X_scaled)
    
    # Inverse transform to get original scale
    prediction = scaler_y.inverse_transform(prediction_scaled)
    
    # Return as dictionary
    return {
        'fouling_resistance': prediction[0, 0],
        'q_shell': prediction[0, 1],
        'q_tube': prediction[0, 2]
    }

def create_sample_data():
    """
    Create sample 14-day operational data for demonstration
    """
    np.random.seed(42)  # For reproducible results
    
    # Create realistic sample data based on typical heat exchanger operations
    days = 14
    
    # Base values with some variation
    unit_charge = np.random.normal(85, 5, days)  # 85% ± 5%
    shell_flow = np.random.normal(12000, 1000, days)  # 12000 ± 1000 kg/h
    shell_temp_in = np.random.normal(450, 20, days)  # 450 ± 20°C
    shell_temp_out = np.random.normal(320, 15, days)  # 320 ± 15°C
    tube_flow = np.random.normal(12000, 1000, days)  # 12000 ± 1000 kg/h
    tube_temp_in = np.random.normal(190, 10, days)  # 190 ± 10°C
    tube_temp_out = np.random.normal(340, 15, days)  # 340 ± 15°C
    
    # Ensure realistic bounds
    unit_charge = np.clip(unit_charge, 70, 95)
    shell_flow = np.clip(shell_flow, 8000, 16000)
    shell_temp_in = np.clip(shell_temp_in, 400, 500)
    shell_temp_out = np.clip(shell_temp_out, 280, 360)
    tube_flow = np.clip(tube_flow, 8000, 16000)
    tube_temp_in = np.clip(tube_temp_in, 150, 220)
    tube_temp_out = np.clip(tube_temp_out, 300, 380)
    
    # Combine into array
    data = np.column_stack([
        unit_charge, shell_flow, shell_temp_in, shell_temp_out,
        tube_flow, tube_temp_in, tube_temp_out
    ])
    
    return data

def visualize_predictions(predictions, data_14_days):
    """
    Create comprehensive visualizations for the predictions
    """
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Multi-Target Heat Exchanger Predictions', fontsize=16, fontweight='bold')
    
    # Target names and values
    targets = [
        ('Fouling Resistance', predictions['fouling_resistance'], 'm² °C/W'),
        ('Q Shell Side', predictions['q_shell'], 'kW'),
        ('Q Tube Side', predictions['q_tube'], 'kW')
    ]
    
    colors = ['red', 'blue', 'green']
    
    # Top row: Prediction bars
    for i, ((name, value, unit), color) in enumerate(zip(targets, colors)):
        ax = axes[0, i]
        
        # Create bar chart
        bars = ax.bar([name], [value], color=color, alpha=0.7, width=0.6, 
                      edgecolor='black', linewidth=1)
        
        # Set proper y-axis limits
        ax.set_ylim(0, max(value * 1.3, 0.001))
        
        # Add value text on bar
        ax.text(0, value/2, f'{value:.6f}' if 'Resistance' in name else f'{value:.2f}', 
                ha='center', va='center', fontweight='bold', fontsize=11, color='white')
        
        ax.set_ylabel(f'{name} ({unit})')
        ax.set_title(f'Predicted {name}')
        ax.grid(True, alpha=0.3)
    
    # Bottom row: Input data trends
    variable_names = ['Unit Charge (%)', 'Shell Flow (kg/h)', 'Shell Temp In (°C)', 
                     'Shell Temp Out (°C)', 'Tube Flow (kg/h)', 'Tube Temp In (°C)', 'Tube Temp Out (°C)']
    
    # Plot 3 most important variables
    important_vars = [0, 2, 6]  # Unit Charge, Shell Temp In, Tube Temp Out
    
    for i, var_idx in enumerate(important_vars):
        ax = axes[1, i]
        days = range(1, 15)
        ax.plot(days, data_14_days[:, var_idx], marker='o', linewidth=2, markersize=6, color=colors[i])
        ax.set_xlabel('Day')
        ax.set_ylabel(variable_names[var_idx])
        ax.set_title(f'14-Day Trend: {variable_names[var_idx]}')
        ax.grid(True, alpha=0.3)
        ax.set_xticks(range(1, 15, 2))
    
    plt.tight_layout()
    plt.show()
    
    # Create interpretation chart
    fig2, ax = plt.subplots(1, 1, figsize=(12, 8))
    fig2.suptitle('Prediction Interpretation', fontsize=16, fontweight='bold')
    
    # Create interpretation ranges
    fouling_ranges = [
        (0, 0.002, 'Excellent', 'green'),
        (0.002, 0.005, 'Good', 'yellow'),
        (0.005, 0.010, 'Fair', 'orange'),
        (0.010, float('inf'), 'Poor', 'red')
    ]
    
    # Plot fouling resistance interpretation
    fouling_value = predictions['fouling_resistance']
    
    y_pos = 0
    for min_val, max_val, status, color in fouling_ranges:
        width = min(max_val, 0.015) - min_val if max_val != float('inf') else 0.005
        ax.barh(y_pos, width, left=min_val, color=color, alpha=0.6, height=0.3)
        ax.text(min_val + width/2, y_pos, status, ha='center', va='center', fontweight='bold')
        y_pos += 0.4
    
    # Mark current prediction
    ax.axvline(x=fouling_value, color='black', linestyle='--', linewidth=3, label=f'Prediction: {fouling_value:.6f}')
    
    ax.set_xlabel('Fouling Resistance (m² °C/W)')
    ax.set_title('Fouling Resistance Interpretation')
    ax.set_xlim(0, 0.015)
    ax.set_ylim(-0.2, 1.8)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def main():
    """
    Main function to demonstrate multi-target prediction
    """
    print("🔥 Multi-Target Heat Exchanger Prediction System")
    print("=" * 60)
    
    # Create sample data
    print("📊 Generating sample 14-day operational data...")
    sample_data = create_sample_data()
    
    print("\nSample data (first 3 days):")
    print("Day | Unit Charge | Shell Flow | Shell T_in | Shell T_out | Tube Flow | Tube T_in | Tube T_out")
    print("-" * 95)
    for i in range(3):
        print(f"{i+1:2d}  | {sample_data[i, 0]:10.1f} | {sample_data[i, 1]:9.0f} | "
              f"{sample_data[i, 2]:9.1f} | {sample_data[i, 3]:10.1f} | {sample_data[i, 4]:8.0f} | "
              f"{sample_data[i, 5]:8.1f} | {sample_data[i, 6]:9.1f}")
    print("... (showing first 3 of 14 days)")
    
    # Make predictions
    print("\n🤖 Making predictions...")
    predictions = predict_multi_targets(sample_data)
    
    if predictions is None:
        print("❌ Prediction failed!")
        return
    
    # Display results
    print("\n✅ Prediction Results:")
    print("=" * 40)
    print(f"🔴 Fouling Resistance: {predictions['fouling_resistance']:.6f} m² °C/W")
    print(f"🔵 Q Shell Side:       {predictions['q_shell']:.2f} kW")
    print(f"🟢 Q Tube Side:        {predictions['q_tube']:.2f} kW")
    
    # Interpret fouling resistance
    fouling = predictions['fouling_resistance']
    if fouling < 0.002:
        status = "🟢 EXCELLENT - Very clean heat exchanger"
    elif fouling < 0.005:
        status = "🟡 GOOD - Normal fouling levels"
    elif fouling < 0.010:
        status = "🟠 FAIR - Moderate fouling, monitor closely"
    else:
        status = "🔴 POOR - High fouling, cleaning recommended"
    
    print(f"\n📊 Fouling Status: {status}")
    
    # Create visualizations
    print("\n📈 Creating visualizations...")
    visualize_predictions(predictions, sample_data)
    
    print("\n🎉 Multi-target prediction complete!")
    print("\nThe model successfully predicted:")
    print("  • Fouling resistance for maintenance planning")
    print("  • Heat duty on shell side for process optimization")
    print("  • Heat duty on tube side for energy balance")

if __name__ == "__main__":
    main()
