#!/usr/bin/env python3
"""
Simple test script for fouling factor predictions
Uses the functions from neuralNetwork.py
"""

import numpy as np
import matplotlib.pyplot as plt
from neuralNetwork import test_single_prediction, test_new_data

def test_with_sample_data():
    """
    Test the model with realistic sample data
    """
    print("🧪 Testing Fouling Factor Prediction Model")
    print("=" * 60)
    
    # Sample 14 days of realistic operational data
    # Format: [Unit_Charge(%), Shell_Flow(kg/h), Shell_Temp_In(°C), Shell_Temp_Out(°C), 
    #          Tube_Flow(kg/h), Tube_Temp_In(°C), Tube_Temp_Out(°C)]
    sample_data = np.array([
        [85.2, 1205.3, 182.1, 176.8, 798.5, 24.8, 44.2],  # Day 1
        [84.8, 1198.7, 181.5, 176.2, 802.1, 25.1, 44.8],  # Day 2
        [86.1, 1212.4, 183.2, 177.5, 795.3, 24.5, 43.9],  # Day 3
        [85.5, 1201.8, 182.8, 177.1, 799.7, 25.3, 45.1],  # Day 4
        [84.9, 1195.2, 181.9, 176.4, 803.2, 24.9, 44.5],  # Day 5
        [85.8, 1208.6, 182.5, 177.0, 797.8, 25.0, 44.7],  # Day 6
        [86.3, 1215.1, 183.7, 178.2, 794.6, 24.7, 43.8],  # Day 7
        [85.1, 1199.4, 182.0, 176.7, 801.5, 25.2, 44.9],  # Day 8
        [84.7, 1193.8, 181.3, 175.9, 804.8, 24.8, 44.3],  # Day 9
        [85.9, 1209.7, 182.9, 177.4, 796.2, 25.1, 44.6],  # Day 10
        [86.0, 1211.3, 183.1, 177.8, 795.9, 24.9, 44.1],  # Day 11
        [85.4, 1203.5, 182.3, 176.9, 798.1, 25.0, 44.8],  # Day 12
        [84.6, 1191.2, 181.1, 175.7, 805.3, 24.7, 44.2],  # Day 13
        [85.7, 1207.8, 182.7, 177.2, 797.4, 25.2, 44.9],  # Day 14
    ])
    
    print(f"📊 Sample data shape: {sample_data.shape}")
    print("Testing with 14 days of operational data...")
    
    # Make prediction
    prediction = test_single_prediction(sample_data)
    
    if prediction is not None:
        print(f"\n🎯 Predicted fouling factor: {prediction:.6f} m² °C/W")
        
        # Interpretation
        print("\n📊 Interpretation:")
        if prediction < 0.0005:
            print("   ✅ Low fouling - Good heat exchanger performance")
            status = "Good"
            color = "green"
        elif prediction < 0.001:
            print("   ⚠️  Moderate fouling - Monitor closely")
            status = "Monitor"
            color = "orange"
        else:
            print("   🚨 High fouling - Consider cleaning or maintenance")
            status = "Action Required"
            color = "red"
        
        # Create visualization
        create_prediction_visualization(sample_data, prediction, status, color)
        
        return prediction
    else:
        print("❌ Prediction failed")
        return None


def create_prediction_visualization(data, prediction, status, color):
    """
    Create visualization of the input data and prediction
    """
    plt.figure(figsize=(15, 10))
    
    days = range(1, 15)
    
    # Plot 1: Unit Charge
    plt.subplot(2, 3, 1)
    plt.plot(days, data[:, 0], 'b-o', linewidth=2, markersize=6)
    plt.xlabel('Day')
    plt.ylabel('Unit Charge (%)')
    plt.title('Unit Charge Over 14 Days')
    plt.grid(True, alpha=0.3)
    
    # Plot 2: Shell Side Temperatures
    plt.subplot(2, 3, 2)
    plt.plot(days, data[:, 2], 'r-o', label='Inlet', linewidth=2, markersize=6)
    plt.plot(days, data[:, 3], 'b-o', label='Outlet', linewidth=2, markersize=6)
    plt.xlabel('Day')
    plt.ylabel('Temperature (°C)')
    plt.title('Shell Side Temperatures')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 3: Tube Side Temperatures
    plt.subplot(2, 3, 3)
    plt.plot(days, data[:, 5], 'g-o', label='Inlet', linewidth=2, markersize=6)
    plt.plot(days, data[:, 6], 'm-o', label='Outlet', linewidth=2, markersize=6)
    plt.xlabel('Day')
    plt.ylabel('Temperature (°C)')
    plt.title('Tube Side Temperatures')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 4: Flow Rates
    plt.subplot(2, 3, 4)
    plt.plot(days, data[:, 1], 'g-o', label='Shell Flow', linewidth=2, markersize=6)
    plt.plot(days, data[:, 4], 'm-o', label='Tube Flow', linewidth=2, markersize=6)
    plt.xlabel('Day')
    plt.ylabel('Flow Rate (kg/h)')
    plt.title('Flow Rates')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 5: Temperature Differences
    plt.subplot(2, 3, 5)
    shell_delta = data[:, 2] - data[:, 3]  # Shell side ΔT
    tube_delta = data[:, 6] - data[:, 5]   # Tube side ΔT
    plt.plot(days, shell_delta, 'r-o', label='Shell ΔT', linewidth=2, markersize=6)
    plt.plot(days, tube_delta, 'b-o', label='Tube ΔT', linewidth=2, markersize=6)
    plt.xlabel('Day')
    plt.ylabel('Temperature Difference (°C)')
    plt.title('Temperature Differences')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 6: Prediction Result
    plt.subplot(2, 3, 6)
    plt.bar(['Prediction'], [prediction], color=color, alpha=0.7, width=0.5)
    plt.ylabel('Fouling Factor (m² °C/W)')
    plt.title(f'Prediction: {status}')
    plt.grid(True, alpha=0.3)
    
    # Add prediction value as text
    plt.text(0, prediction + prediction*0.1, f'{prediction:.6f}', 
             ha='center', va='bottom', fontweight='bold', fontsize=12)
    
    plt.tight_layout()
    plt.show()


def test_multiple_scenarios():
    """
    Test multiple operational scenarios
    """
    print("\n" + "=" * 60)
    print("🔬 Testing Multiple Operational Scenarios")
    print("=" * 60)
    
    scenarios = {
        "Normal Operation": {
            "unit_charge": 85.0,
            "shell_flow": 1200.0,
            "shell_temp_in": 182.0,
            "shell_temp_out": 177.0,
            "tube_flow": 800.0,
            "tube_temp_in": 25.0,
            "tube_temp_out": 45.0
        },
        "High Load": {
            "unit_charge": 95.0,
            "shell_flow": 1350.0,
            "shell_temp_in": 185.0,
            "shell_temp_out": 179.0,
            "tube_flow": 900.0,
            "tube_temp_in": 25.0,
            "tube_temp_out": 48.0
        },
        "Low Load": {
            "unit_charge": 70.0,
            "shell_flow": 1000.0,
            "shell_temp_in": 178.0,
            "shell_temp_out": 174.0,
            "tube_flow": 650.0,
            "tube_temp_in": 25.0,
            "tube_temp_out": 40.0
        }
    }
    
    results = {}
    
    for scenario_name, params in scenarios.items():
        print(f"\n📊 Testing {scenario_name}...")
        
        # Create 14 days of data with small variations
        np.random.seed(42)  # For reproducible results
        scenario_data = []
        
        for day in range(14):
            # Add small daily variations (±2% for flows, ±1°C for temperatures)
            daily_data = [
                params["unit_charge"] + np.random.normal(0, 1),
                params["shell_flow"] + np.random.normal(0, params["shell_flow"] * 0.02),
                params["shell_temp_in"] + np.random.normal(0, 1),
                params["shell_temp_out"] + np.random.normal(0, 1),
                params["tube_flow"] + np.random.normal(0, params["tube_flow"] * 0.02),
                params["tube_temp_in"] + np.random.normal(0, 0.5),
                params["tube_temp_out"] + np.random.normal(0, 1)
            ]
            scenario_data.append(daily_data)
        
        scenario_array = np.array(scenario_data)
        prediction = test_single_prediction(scenario_array)
        
        if prediction is not None:
            results[scenario_name] = prediction
            print(f"   Prediction: {prediction:.6f} m² °C/W")
    
    # Compare scenarios
    if results:
        print(f"\n📈 Scenario Comparison:")
        print("-" * 40)
        sorted_results = sorted(results.items(), key=lambda x: x[1])
        
        for scenario, pred in sorted_results:
            print(f"{scenario:15s}: {pred:.6f} m² °C/W")
        
        # Plot comparison
        plt.figure(figsize=(10, 6))
        scenarios_list = list(results.keys())
        predictions_list = list(results.values())
        
        colors = ['green' if p < 0.0005 else 'orange' if p < 0.001 else 'red' for p in predictions_list]
        
        bars = plt.bar(scenarios_list, predictions_list, color=colors, alpha=0.7)
        plt.ylabel('Predicted Fouling Factor (m² °C/W)')
        plt.title('Fouling Factor Predictions for Different Operating Scenarios')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, pred in zip(bars, predictions_list):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + bar.get_height()*0.01,
                    f'{pred:.6f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.show()


def show_usage_instructions():
    """
    Show instructions for using the model with your own data
    """
    print("\n" + "=" * 60)
    print("📝 How to Use This Model with Your Own Data")
    print("=" * 60)
    
    print("""
🔧 Method 1: Using CSV Files
   1. Format your data like the training data:
      - Semicolon (;) separated values
      - Columns: Date, Unit Charge (%), Shell Flow (kg/h), Shell Temp In (°C), 
                Shell Temp Out (°C), Tube Flow (kg/h), Tube Temp In (°C), 
                Tube Temp Out (°C), Fouling Resistance (dummy values)
   2. Run: python3 neuralNetwork.py test your_data.csv

🔧 Method 2: Using Python Code
   ```python
   import numpy as np
   from neuralNetwork import test_single_prediction
   
   # Your 14 days of data (14 rows × 7 columns)
   your_data = np.array([
       [day1_unit_charge, day1_shell_flow, day1_shell_temp_in, day1_shell_temp_out, 
        day1_tube_flow, day1_tube_temp_in, day1_tube_temp_out],
       # ... continue for 14 days
   ])
   
   prediction = test_single_prediction(your_data)
   print(f"Predicted fouling factor: {prediction:.6f} m² °C/W")
   ```

📊 Variable Meanings:
   • Unit Charge (%): Operating capacity percentage
   • Shell Flow (kg/h): Reactor effluent flow rate
   • Shell Temp In/Out (°C): Shell side inlet/outlet temperatures
   • Tube Flow (kg/h): Reactor feed flow rate  
   • Tube Temp In/Out (°C): Tube side inlet/outlet temperatures

⚠️  Important Notes:
   • Need exactly 14 consecutive days of data
   • Use the same units as training data
   • Model predicts fouling factor in m² °C/W
   • Values < 0.0005 indicate good performance
   • Values > 0.001 may require maintenance
""")


if __name__ == "__main__":
    # Run the main test
    print("🚀 Starting Fouling Factor Prediction Tests")
    
    # Test 1: Basic prediction with sample data
    prediction = test_with_sample_data()
    
    if prediction is not None:
        # Test 2: Multiple scenarios
        test_multiple_scenarios()
        
        # Test 3: Show usage instructions
        show_usage_instructions()
        
        print("\n🎉 All tests completed successfully!")
        print("\nNext steps:")
        print("1. Replace sample data with your actual plant measurements")
        print("2. Run this script again to get real predictions")
        print("3. Set up automated monitoring using the CSV testing method")
        print("4. Consider retraining the model with more recent data periodically")
    else:
        print("\n❌ Tests failed. Please check that the model files exist:")
        print("   - fouling_prediction_model.model")
        print("   - scalers.pkl")
        print("\nRun training first: python3 neuralNetwork.py")
